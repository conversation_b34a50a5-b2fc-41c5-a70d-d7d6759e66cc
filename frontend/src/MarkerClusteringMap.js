import React, { useState, useEffect } from 'react';
import DeliveryAssignmentModal from './components/DeliveryAssignmentModal';
import EnterpriseClusterDashboard from './components/EnterpriseClusterDashboard';
import {
    AgentSidebar,
    useAgentData,
    useMapCluster
} from './MarkerClusteringMap/index';

const MarkerClusteringMap = ({ onUserSelect, onNavigateToDashboard }) => {
    const [selectedAgent, setSelectedAgent] = useState(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [showAssignmentModal, setShowAssignmentModal] = useState(false);
    const [agentToAssign, setAgentToAssign] = useState(null);
    const [statusFilter, setStatusFilter] = useState('all'); // 'all', 'present', 'absent'
    const [viewMode, setViewMode] = useState('enterprise'); // 'enterprise' or 'map'

    // Custom hooks
    const {
        agents,
        loading,
        error,
        refreshAgentData,
        getFilteredAgents
    } = useAgentData();

    const {
        mapContainerRef,
        isMapInitializing,
        initializeMap,
        updateMapWithAgents
    } = useMapCluster();

    // Get filtered agents
    const filteredAgents = getFilteredAgents(statusFilter, searchTerm);

    // Event handlers
    const handleAgentSelect = (agent) => {
        console.log('🔍 Selected agent:', agent);
        setSelectedAgent(agent);
        if (onUserSelect) {
            onUserSelect(agent.agent_id, agent);
        }
    };

    const handleAssignDelivery = (agent) => {
        console.log('🚚 Assign delivery to agent:', agent);
        setAgentToAssign(agent);
        setShowAssignmentModal(true);
    };

    // Setup global window functions for popup buttons
    useEffect(() => {
        window.onAgentSelectFromPopup = (agentId) => {
            const agent = agents.find(a => a.agent_id === agentId);
            if (agent) handleAgentSelect(agent);
        };

        window.onAssignDeliveryFromPopup = (agentId) => {
            const agent = agents.find(a => a.agent_id === agentId);
            if (agent) handleAssignDelivery(agent);
        };

        return () => {
            delete window.onAgentSelectFromPopup;
            delete window.onAssignDeliveryFromPopup;
        };
    }, [agents, handleAgentSelect]);

    // Initialize map when agents are loaded
    useEffect(() => {
        if (agents.length > 0 && !isMapInitializing) {
            initializeMap(agents, handleAgentSelect);
        }
    }, [agents, isMapInitializing, initializeMap, handleAgentSelect]);

    // Update map when filtered agents change
    useEffect(() => {
        if (filteredAgents.length > 0) {
            updateMapWithAgents(filteredAgents, handleAgentSelect);
        }
    }, [filteredAgents, updateMapWithAgents, handleAgentSelect]);

    if (viewMode === 'enterprise') {
        return (
            <>
                {/* Enterprise Dashboard View */}
                <EnterpriseClusterDashboard
                    agents={filteredAgents}
                    selectedAgent={selectedAgent}
                    onAgentSelect={handleAgentSelect}
                    onAssignDelivery={handleAssignDelivery}
                    searchTerm={searchTerm}
                    onSearchChange={setSearchTerm}
                    statusFilter={statusFilter}
                    onStatusFilterChange={setStatusFilter}
                    onViewModeChange={setViewMode}
                />

                {/* Modern Floating Action Button */}
                <button
                    onClick={() => setViewMode('map')}
                    className="modern-fab"
                    style={{
                        position: 'fixed',
                        bottom: '24px',
                        right: '24px',
                        width: '64px',
                        height: '64px',
                        background: 'linear-gradient(135deg, #1890ff 0%, #722ed1 100%)',
                        color: 'white',
                        border: 'none',
                        borderRadius: '50%',
                        fontSize: '24px',
                        cursor: 'pointer',
                        boxShadow: '0 8px 32px rgba(24, 144, 255, 0.4)',
                        zIndex: 1000,
                        transition: 'all 0.3s ease',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        backdropFilter: 'blur(10px)',
                        border: '2px solid rgba(255, 255, 255, 0.1)'
                    }}
                    onMouseEnter={(e) => {
                        e.target.style.transform = 'scale(1.1) translateY(-2px)';
                        e.target.style.boxShadow = '0 12px 40px rgba(24, 144, 255, 0.6)';
                    }}
                    onMouseLeave={(e) => {
                        e.target.style.transform = 'scale(1) translateY(0)';
                        e.target.style.boxShadow = '0 8px 32px rgba(24, 144, 255, 0.4)';
                    }}
                    title="Switch to Map View"
                >
                    🗺️
                </button>

                {/* Delivery Assignment Modal */}
                <DeliveryAssignmentModal
                    isOpen={showAssignmentModal}
                    onClose={() => {
                        setShowAssignmentModal(false);
                        setAgentToAssign(null);
                    }}
                    selectedAgent={agentToAssign}
                    selectedLocation={null}
                    onAssign={(assignmentData) => {
                        console.log('✅ Delivery assigned successfully:', assignmentData);
                        refreshAgentData();
                    }}
                />
            </>
        );
    }

    return (
        <div style={{ width: '100%', height: '100vh', position: 'relative', display: 'flex' }}>
            {/* Map Container */}
            <div ref={mapContainerRef} style={{ flex: 1, height: '100%' }} />

            {/* Loading Overlay */}
            {loading && (
                <div style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    backgroundColor: 'rgba(255, 255, 255, 0.9)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    zIndex: 1000
                }}>
                    <div style={{ textAlign: 'center' }}>
                        <div style={{ fontSize: '18px', marginBottom: '8px' }}>🗺️</div>
                        <div style={{ fontSize: '14px', color: '#666' }}>Loading User Map...</div>
                    </div>
                </div>
            )}

            {/* Modern Dashboard Toggle */}
            <button
                onClick={() => setViewMode('enterprise')}
                className="modern-dashboard-toggle"
                style={{
                    position: 'absolute',
                    top: '24px',
                    left: '24px',
                    background: 'linear-gradient(135deg, #1890ff 0%, #722ed1 100%)',
                    color: 'white',
                    border: 'none',
                    borderRadius: '16px',
                    padding: '12px 20px',
                    fontSize: '14px',
                    fontWeight: '600',
                    cursor: 'pointer',
                    boxShadow: '0 6px 24px rgba(24, 144, 255, 0.4)',
                    zIndex: 1000,
                    transition: 'all 0.3s ease',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    backdropFilter: 'blur(10px)',
                    border: '2px solid rgba(255, 255, 255, 0.1)',
                    fontFamily: 'inherit'
                }}
                onMouseEnter={(e) => {
                    e.target.style.transform = 'translateY(-2px) scale(1.05)';
                    e.target.style.boxShadow = '0 8px 32px rgba(24, 144, 255, 0.5)';
                }}
                onMouseLeave={(e) => {
                    e.target.style.transform = 'translateY(0) scale(1)';
                    e.target.style.boxShadow = '0 6px 24px rgba(24, 144, 255, 0.4)';
                }}
                title="Switch to Dashboard View"
            >
                <span style={{ fontSize: '16px' }}>📊</span>
                <span>Dashboard</span>
            </button>

            {/* Navigate to Dashboard Route */}
            {onNavigateToDashboard && (
                <button
                    onClick={onNavigateToDashboard}
                    style={{
                        position: 'absolute',
                        top: '24px',
                        left: '200px',
                        background: 'linear-gradient(135deg, #28a745 0%, #20c997 100%)',
                        color: 'white',
                        border: 'none',
                        borderRadius: '16px',
                        padding: '12px 20px',
                        fontSize: '14px',
                        fontWeight: '600',
                        cursor: 'pointer',
                        boxShadow: '0 6px 24px rgba(40, 167, 69, 0.4)',
                        zIndex: 1000,
                        transition: 'all 0.3s ease',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '8px',
                        backdropFilter: 'blur(10px)',
                        border: '2px solid rgba(255, 255, 255, 0.1)',
                        fontFamily: 'inherit'
                    }}
                    onMouseEnter={(e) => {
                        e.target.style.transform = 'translateY(-2px) scale(1.05)';
                        e.target.style.boxShadow = '0 8px 32px rgba(40, 167, 69, 0.5)';
                    }}
                    onMouseLeave={(e) => {
                        e.target.style.transform = 'translateY(0) scale(1)';
                        e.target.style.boxShadow = '0 6px 24px rgba(40, 167, 69, 0.4)';
                    }}
                    title="Navigate to Dashboard Page"
                >
                    <span style={{ fontSize: '16px' }}>🚀</span>
                    <span>Go to Dashboard</span>
                </button>
            )}

            {/* Agent Sidebar */}
            <AgentSidebar
                agents={agents}
                filteredAgents={filteredAgents}
                selectedAgent={selectedAgent}
                searchTerm={searchTerm}
                statusFilter={statusFilter}
                loading={loading}
                error={error}
                onSearchChange={setSearchTerm}
                onStatusFilterChange={setStatusFilter}
                onAgentSelect={handleAgentSelect}
                onAssignDelivery={handleAssignDelivery}
                onRefresh={refreshAgentData}
            />

            {/* Delivery Assignment Modal */}
            <DeliveryAssignmentModal
                isOpen={showAssignmentModal}
                onClose={() => {
                    setShowAssignmentModal(false);
                    setAgentToAssign(null);
                }}
                selectedAgent={agentToAssign}
                selectedLocation={null}
                onAssign={(assignmentData) => {
                    console.log('✅ Delivery assigned successfully:', assignmentData);
                    refreshAgentData();
                }}
            />
        </div>
    );
};

export default MarkerClusteringMap;
