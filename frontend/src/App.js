import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import DeliveryTrackingApp from './DeliveryTrackingApp';

function App() {
  return (
    <div className="App">
      <Router>
        <Routes>
          {/* Redirect root to cluster */}
          <Route path="/" element={<DeliveryTrackingApp />} />

          {/* Cluster screen */}
          <Route path="/cluster" element={<DeliveryTrackingApp />} />

          {/* Dashboard screen */}
          <Route path="/dashboard" element={<DeliveryTrackingApp />} />

          {/* Individual agent tracking */}
          <Route path="/agent/:agentId" element={<DeliveryTrackingApp />} />
        </Routes>
      </Router>
    </div>
  );
}

export default App;
