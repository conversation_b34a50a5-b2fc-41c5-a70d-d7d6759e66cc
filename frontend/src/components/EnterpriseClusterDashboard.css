/* Enterprise Dashboard Styling */
.enterprise-dashboard {
    min-height: 100vh;
    background: linear-gradient(135deg, #f0f2f5 0%, #e6f7ff 100%);
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    padding: 24px;
}

/* Header Section */
.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32px;
    padding: 24px 32px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(24, 144, 255, 0.1);
}

.header-main h1 {
    margin: 0;
    font-size: 32px;
    font-weight: 700;
    color: #1890ff;
    margin-bottom: 8px;
}

.header-main p {
    margin: 0;
    color: #8c8c8c;
    font-size: 16px;
    font-weight: 500;
}

.header-time {
    text-align: right;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 4px;
}

.current-time {
    font-size: 28px;
    font-weight: 700;
    color: #1890ff;
    font-family: 'Monaco', '<PERSON><PERSON>', monospace;
    background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.current-date {
    font-size: 14px;
    color: #8c8c8c;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Stats Grid - Redesigned for No Horizontal Scroll */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
    margin-bottom: 32px;
    max-width: 100%;
    overflow: hidden;
}

/* Responsive breakpoints for stats */
@media (max-width: 1400px) {
    .stats-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 1024px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }
}

.stat-card {
    background: white;
    border-radius: 16px;
    padding: 24px;
    display: flex;
    align-items: center;
    gap: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(24, 144, 255, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #1890ff, #722ed1);
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(24, 144, 255, 0.15);
}

.stat-icon {
    font-size: 32px;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #1890ff, #722ed1);
    border-radius: 12px;
    color: white;
    box-shadow: 0 4px 16px rgba(24, 144, 255, 0.3);
}

.stat-content h3 {
    margin: 0;
    font-size: 32px;
    font-weight: 700;
    color: #262626;
    line-height: 1;
}

.stat-content p {
    margin: 4px 0 8px 0;
    color: #8c8c8c;
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-badge {
    font-size: 12px;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 6px;
    display: inline-block;
}

.stat-badge.success {
    background: rgba(82, 196, 26, 0.1);
    color: #52c41a;
}

.stat-badge.warning {
    background: rgba(250, 173, 20, 0.1);
    color: #faad14;
}

.stat-badge.error {
    background: rgba(255, 77, 79, 0.1);
    color: #ff4d4f;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: #f0f0f0;
    border-radius: 3px;
    overflow: hidden;
    margin-top: 8px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #52c41a, #73d13d);
    border-radius: 3px;
    transition: width 0.3s ease;
}

/* Dashboard Content - Redesigned for Better UX */
.dashboard-content {
    display: grid;
    grid-template-columns: 1fr 380px;
    gap: 20px;
    max-width: 100%;
    overflow: hidden;
}

/* Responsive dashboard layout */
@media (max-width: 1200px) {
    .dashboard-content {
        grid-template-columns: 1fr 320px;
        gap: 16px;
    }
}

@media (max-width: 1024px) {
    .dashboard-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }
}

/* Panel Styling */
.left-panel, .right-panel {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(24, 144, 255, 0.1);
    overflow: hidden;
}

.panel-header {
    padding: 20px 24px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, rgba(24, 144, 255, 0.02) 0%, rgba(114, 46, 209, 0.02) 100%);
}

.panel-header h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 700;
    color: #262626;
}

/* Search and Filter */
.search-container {
    padding: 20px 24px;
    border-bottom: 1px solid #f0f0f0;
}

.search-input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #d9d9d9;
    border-radius: 8px;
    font-size: 14px;
    background: #fafafa;
    transition: all 0.2s ease;
}

.search-input:focus {
    outline: none;
    border-color: #1890ff;
    background: white;
    box-shadow: 0 0 0 4px rgba(24, 144, 255, 0.1);
}

.filter-select {
    padding: 8px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    background: white;
    font-size: 14px;
    font-weight: 500;
    color: #262626;
    cursor: pointer;
}

.filter-select:focus {
    outline: none;
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

/* Agent Cards - Redesigned for Better UX */
.agents-list {
    max-height: 600px;
    overflow-y: auto;
    padding: 16px 20px;
}

.agent-card {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 20px;
    border-radius: 16px;
    margin-bottom: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;
    background: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.agent-card:hover {
    background: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(24, 144, 255, 0.12);
    border-color: rgba(24, 144, 255, 0.2);
}

.agent-card.selected {
    background: rgba(24, 144, 255, 0.02);
    border-color: #1890ff;
    box-shadow: 0 8px 24px rgba(24, 144, 255, 0.15);
}

/* Agent card header - horizontal layout for basic info */
.agent-card-header {
    display: flex;
    align-items: center;
    gap: 16px;
}

/* Agent card body - vertical layout for details */
.agent-card-body {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

/* Agent Avatar - Enhanced Design */
.agent-avatar {
    position: relative;
    width: 56px;
    height: 56px;
    flex-shrink: 0;
}

.agent-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 16px;
    object-fit: cover;
    border: 3px solid white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.status-indicator {
    position: absolute;
    bottom: -2px;
    right: -2px;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    border: 3px solid white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Agent Card Header Layout */
.agent-card-header {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 12px;
}

/* Agent Card Body Layout */
.agent-card-body {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

/* Agent Info - Enhanced Layout */
.agent-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.agent-name {
    font-weight: 700;
    font-size: 18px;
    color: #262626;
    margin: 0;
    line-height: 1.2;
}

.agent-role {
    font-size: 13px;
    color: #8c8c8c;
    font-weight: 500;
    margin: 0;
}

/* Agent Status Badge - Redesigned */
.agent-status-badge {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    white-space: nowrap;
    flex-shrink: 0;
}

.agent-status-badge.success {
    background: rgba(82, 196, 26, 0.1);
    color: #52c41a;
    border: 1px solid rgba(82, 196, 26, 0.2);
}

.agent-status-badge.warning {
    background: rgba(250, 173, 20, 0.1);
    color: #faad14;
    border: 1px solid rgba(250, 173, 20, 0.2);
}

.agent-status-badge.error {
    background: rgba(255, 77, 79, 0.1);
    color: #ff4d4f;
    border: 1px solid rgba(255, 77, 79, 0.2);
}

.agent-status-badge.purple {
    background: rgba(114, 46, 209, 0.1);
    color: #722ed1;
    border: 1px solid rgba(114, 46, 209, 0.2);
}

/* Agent Metrics - Enhanced Design */
.agent-metrics {
    display: flex;
    gap: 20px;
    font-size: 13px;
    color: #595959;
    font-weight: 500;
    padding: 8px 0;
}

.agent-metrics span {
    display: flex;
    align-items: center;
    gap: 6px;
}

/* Redesigned Action Buttons - Natural Flow */
.agent-actions {
    display: flex;
    gap: 12px;
    margin-top: 8px;
}

.assign-btn, .track-btn {
    flex: 1;
    padding: 12px 16px;
    border: none;
    border-radius: 10px;
    font-size: 13px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    text-transform: none;
    letter-spacing: 0;
}

.assign-btn {
    background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(82, 196, 26, 0.3);
}

.track-btn {
    background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

.assign-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(82, 196, 26, 0.4);
}

.track-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
}

/* Button icons */
.btn-icon {
    font-size: 14px;
}

/* Mobile responsive buttons */
@media (max-width: 768px) {
    .agent-actions {
        flex-direction: column;
        gap: 8px;
    }

    .assign-btn, .track-btn {
        flex: none;
        width: 100%;
    }
}

/* Live Updates */
.live-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #52c41a;
    font-weight: 600;
    font-size: 14px;
}

.pulse-dot {
    width: 8px;
    height: 8px;
    background: #52c41a;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.2); opacity: 0.7; }
    100% { transform: scale(1); opacity: 1; }
}

.updates-list {
    padding: 20px 24px;
    max-height: 400px;
    overflow-y: auto;
}

.update-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 12px;
    border-radius: 8px;
    margin-bottom: 12px;
    border-left: 4px solid;
}

.update-item.success {
    border-left-color: #52c41a;
    background: rgba(82, 196, 26, 0.05);
}

.update-item.info {
    border-left-color: #1890ff;
    background: rgba(24, 144, 255, 0.05);
}

.update-item.warning {
    border-left-color: #faad14;
    background: rgba(250, 173, 20, 0.05);
}

.update-icon {
    font-size: 16px;
    margin-top: 2px;
}

.update-content p {
    margin: 0 0 4px 0;
    font-size: 14px;
    font-weight: 500;
    color: #262626;
}

.update-time {
    font-size: 12px;
    color: #8c8c8c;
}

/* Enterprise Features */
.enterprise-features {
    padding: 24px;
    border-top: 1px solid #f0f0f0;
    background: linear-gradient(135deg, rgba(24, 144, 255, 0.05) 0%, rgba(114, 46, 209, 0.05) 100%);
}

.enterprise-features h3 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 700;
    color: #262626;
}

.feature-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-bottom: 20px;
}

.feature-btn {
    padding: 14px 16px;
    border: none;
    border-radius: 10px;
    font-size: 13px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.feature-btn.temperature {
    background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);
    color: white;
}

.feature-btn.predictions {
    background: linear-gradient(135deg, #4ecdc4 0%, #6ee7df 100%);
    color: white;
}

.feature-btn.compliance {
    background: linear-gradient(135deg, #45b7d1 0%, #67c3e0 100%);
    color: white;
}

.feature-btn.inventory {
    background: linear-gradient(135deg, #f9ca24 0%, #f0932b 100%);
    color: white;
}

.feature-btn.analytics {
    background: linear-gradient(135deg, #722ed1 0%, #9254de 100%);
    color: white;
}

.feature-btn.geofencing {
    background: linear-gradient(135deg, #13c2c2 0%, #36cfc9 100%);
    color: white;
}

.feature-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

/* Quick Actions */
.quick-actions {
    padding: 24px;
    border-top: 1px solid #f0f0f0;
    background: linear-gradient(135deg, rgba(24, 144, 255, 0.02) 0%, rgba(114, 46, 209, 0.02) 100%);
}

.quick-actions h3 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 700;
    color: #262626;
}

.action-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.action-btn {
    padding: 12px 16px;
    border: none;
    border-radius: 8px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
}

.action-btn.emergency {
    background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
    color: white;
}

.action-btn.broadcast {
    background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
    color: white;
}

.action-btn.optimize {
    background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
    color: white;
}

.action-btn.report {
    background: linear-gradient(135deg, #722ed1 0%, #9254de 100%);
    color: white;
}

.action-btn.maintenance {
    background: linear-gradient(135deg, #fa8c16 0%, #ffa940 100%);
    color: white;
}

.action-btn.notifications {
    background: linear-gradient(135deg, #eb2f96 0%, #f759ab 100%);
    color: white;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* Enhanced Responsive Design */
@media (max-width: 1200px) {
    .enterprise-dashboard {
        padding: 20px;
    }

    .dashboard-header {
        flex-direction: column;
        text-align: center;
        gap: 16px;
    }

    .header-time {
        align-items: center;
    }
}

@media (max-width: 768px) {
    .enterprise-dashboard {
        padding: 16px;
    }

    .dashboard-header {
        padding: 20px;
    }

    .header-main h1 {
        font-size: 24px;
    }

    .header-main p {
        font-size: 14px;
    }

    .current-time {
        font-size: 24px;
    }

    .current-date {
        font-size: 12px;
    }

    .stat-card {
        padding: 20px;
        gap: 16px;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 24px;
    }

    .stat-content h3 {
        font-size: 24px;
    }

    .agents-list {
        padding: 12px 16px;
    }

    .agent-card {
        padding: 16px;
        margin-bottom: 12px;
    }

    .agent-card-header {
        gap: 12px;
    }

    .agent-avatar img {
        width: 40px;
        height: 40px;
    }

    .agent-name {
        font-size: 14px;
    }

    .feature-buttons, .action-buttons {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .feature-btn, .action-btn {
        padding: 12px 16px;
        font-size: 12px;
    }
}

@media (max-width: 480px) {
    .enterprise-dashboard {
        padding: 12px;
    }

    .dashboard-header {
        padding: 16px;
    }

    .stats-grid {
        gap: 8px;
    }

    .stat-card {
        padding: 16px;
        gap: 12px;
    }

    .dashboard-content {
        gap: 16px;
    }

    .agent-card {
        padding: 12px;
    }

    .agent-actions {
        gap: 8px;
    }

    .assign-btn, .track-btn {
        padding: 10px 12px;
        font-size: 12px;
    }
}




