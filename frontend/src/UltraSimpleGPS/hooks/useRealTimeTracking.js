/**
 * Real-Time Tracking Hook
 * 
 * React hook for managing real-time GPS tracking with Server-Sent Events
 * Provides enterprise-grade real-time location updates with automatic reconnection
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import realTimeTrackingService from '../services/RealTimeTrackingService';

export const useRealTimeTracking = (agentId, options = {}) => {
    const {
        autoConnect = true,
        onLocationUpdate = null,
        onConnectionChange = null,
        enableLogging = true
    } = options;

    // State
    const [isConnected, setIsConnected] = useState(false);
    const [connectionStatus, setConnectionStatus] = useState('disconnected');
    const [lastUpdate, setLastUpdate] = useState(null);
    const [reconnectAttempts, setReconnectAttempts] = useState(0);
    const [error, setError] = useState(null);

    // Refs to store callbacks and prevent stale closures
    const onLocationUpdateRef = useRef(onLocationUpdate);
    const onConnectionChangeRef = useRef(onConnectionChange);

    // Update refs when callbacks change
    useEffect(() => {
        onLocationUpdateRef.current = onLocationUpdate;
    }, [onLocationUpdate]);

    useEffect(() => {
        onConnectionChangeRef.current = onConnectionChange;
    }, [onConnectionChange]);

    /**
     * Handle real-time location updates
     */
    const handleLocationUpdate = useCallback((data) => {
        if (enableLogging) {
            console.log('🎯 Real-time location update received:', data);
        }

        const locationUpdate = {
            agentId: data.agent_id,
            timestamp: new Date(data.timestamp),
            location: {
                latitude: data.location.latitude,
                longitude: data.location.longitude
            },
            speed: data.speed_m_s,
            heading: data.heading_deg,
            accuracy: data.accuracy_m,
            battery: data.battery_pct,
            type: data.type // 'location_update' or 'current_location'
        };

        setLastUpdate(locationUpdate);
        setError(null);

        // Call external callback if provided
        if (onLocationUpdateRef.current) {
            try {
                onLocationUpdateRef.current(locationUpdate);
            } catch (error) {
                console.error('❌ Error in location update callback:', error);
            }
        }
    }, [enableLogging]);

    /**
     * Handle current location (sent when first connecting)
     */
    const handleCurrentLocation = useCallback((data) => {
        if (enableLogging) {
            console.log('📍 Current location received:', data);
        }
        handleLocationUpdate(data);
    }, [handleLocationUpdate, enableLogging]);

    /**
     * Handle connection status changes
     */
    const handleConnectionChange = useCallback((event) => {
        if (enableLogging) {
            console.log('🔗 Connection status changed:', event);
        }

        switch (event.type) {
            case 'connected':
                setIsConnected(true);
                setConnectionStatus('connected');
                setReconnectAttempts(0);
                setError(null);
                break;
            case 'disconnected':
                setIsConnected(false);
                setConnectionStatus('disconnected');
                setError(null);
                break;
            case 'reconnecting':
                setIsConnected(false);
                setConnectionStatus('reconnecting');
                setReconnectAttempts(event.attempt || 0);
                setError(`Reconnecting... (${event.attempt}/${event.maxAttempts})`);
                break;
            default:
                console.warn('Unknown connection event:', event);
        }

        // Call external callback if provided
        if (onConnectionChangeRef.current) {
            try {
                onConnectionChangeRef.current(event);
            } catch (error) {
                console.error('❌ Error in connection change callback:', error);
            }
        }
    }, [enableLogging]);

    /**
     * Connect to real-time tracking
     */
    const connect = useCallback(async () => {
        if (!agentId) {
            console.warn('⚠️ Cannot connect: No agent ID provided');
            return;
        }

        try {
            setError(null);
            await realTimeTrackingService.connect(agentId);
        } catch (error) {
            console.error('❌ Failed to connect to real-time tracking:', error);
            setError(error.message);
        }
    }, [agentId]);

    /**
     * Disconnect from real-time tracking
     */
    const disconnect = useCallback(() => {
        realTimeTrackingService.disconnect();
        setIsConnected(false);
        setConnectionStatus('disconnected');
        setError(null);
    }, []);

    /**
     * Manually reconnect
     */
    const reconnect = useCallback(async () => {
        disconnect();
        setTimeout(() => {
            connect();
        }, 1000);
    }, [connect, disconnect]);

    /**
     * Get detailed connection status
     */
    const getDetailedStatus = useCallback(() => {
        return {
            ...realTimeTrackingService.getConnectionStatus(),
            isConnected,
            connectionStatus,
            reconnectAttempts,
            error,
            lastUpdate
        };
    }, [isConnected, connectionStatus, reconnectAttempts, error, lastUpdate]);

    // Set up event listeners
    useEffect(() => {
        // Add event listeners
        realTimeTrackingService.addEventListener('location_update', handleLocationUpdate);
        realTimeTrackingService.addEventListener('current_location', handleCurrentLocation);
        realTimeTrackingService.addConnectionListener(handleConnectionChange);

        // Cleanup function
        return () => {
            realTimeTrackingService.removeEventListener('location_update', handleLocationUpdate);
            realTimeTrackingService.removeEventListener('current_location', handleCurrentLocation);
            realTimeTrackingService.removeConnectionListener(handleConnectionChange);
        };
    }, [handleLocationUpdate, handleCurrentLocation, handleConnectionChange]);

    // Auto-connect when agent ID changes
    useEffect(() => {
        if (autoConnect && agentId) {
            connect();
        }

        // Cleanup on unmount or agent change
        return () => {
            if (realTimeTrackingService.isConnectedToAgent(agentId)) {
                disconnect();
            }
        };
    }, [agentId, autoConnect, connect, disconnect]);

    // Return hook interface
    return {
        // Connection state
        isConnected,
        connectionStatus,
        reconnectAttempts,
        error,
        
        // Data
        lastUpdate,
        
        // Actions
        connect,
        disconnect,
        reconnect,
        getDetailedStatus,
        
        // Service reference for advanced usage
        service: realTimeTrackingService
    };
};

export default useRealTimeTracking;
