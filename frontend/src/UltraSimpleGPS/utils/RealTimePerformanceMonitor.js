/**
 * Real-Time Performance Monitor
 * 
 * Enterprise-grade performance monitoring for real-time GPS tracking
 * Tracks metrics like update frequency, latency, and connection stability
 */

class RealTimePerformanceMonitor {
    constructor() {
        this.metrics = {
            totalUpdates: 0,
            updateFrequency: 0,
            averageLatency: 0,
            connectionUptime: 0,
            reconnectionCount: 0,
            lastUpdateTime: null,
            connectionStartTime: null,
            latencyHistory: [],
            updateIntervals: []
        };
        
        this.isMonitoring = false;
        this.monitoringInterval = null;
        this.maxHistorySize = 100; // Keep last 100 measurements
    }

    /**
     * Start performance monitoring
     */
    startMonitoring() {
        if (this.isMonitoring) return;
        
        this.isMonitoring = true;
        this.metrics.connectionStartTime = Date.now();
        
        // Update metrics every 5 seconds
        this.monitoringInterval = setInterval(() => {
            this.updateMetrics();
        }, 5000);
        
        console.log('📊 Real-time performance monitoring started');
    }

    /**
     * Stop performance monitoring
     */
    stopMonitoring() {
        if (!this.isMonitoring) return;
        
        this.isMonitoring = false;
        
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }
        
        console.log('📊 Real-time performance monitoring stopped');
    }

    /**
     * Record a location update
     * @param {Object} updateData - Location update data
     */
    recordLocationUpdate(updateData) {
        const now = Date.now();
        const updateTime = new Date(updateData.timestamp).getTime();
        const latency = now - updateTime;
        
        // Update basic metrics
        this.metrics.totalUpdates++;
        this.metrics.lastUpdateTime = now;
        
        // Record latency
        this.metrics.latencyHistory.push(latency);
        if (this.metrics.latencyHistory.length > this.maxHistorySize) {
            this.metrics.latencyHistory.shift();
        }
        
        // Record update interval
        if (this.metrics.lastUpdateTime) {
            const interval = now - this.metrics.lastUpdateTime;
            this.metrics.updateIntervals.push(interval);
            if (this.metrics.updateIntervals.length > this.maxHistorySize) {
                this.metrics.updateIntervals.shift();
            }
        }
        
        // Calculate average latency
        this.metrics.averageLatency = this.metrics.latencyHistory.reduce((sum, lat) => sum + lat, 0) / this.metrics.latencyHistory.length;
        
        console.log(`📊 Location update recorded - Latency: ${latency}ms, Total updates: ${this.metrics.totalUpdates}`);
    }

    /**
     * Record connection event
     * @param {Object} event - Connection event
     */
    recordConnectionEvent(event) {
        switch (event.type) {
            case 'connected':
                this.metrics.connectionStartTime = Date.now();
                break;
            case 'reconnecting':
                this.metrics.reconnectionCount++;
                break;
            case 'disconnected':
                // Calculate uptime if we were connected
                if (this.metrics.connectionStartTime) {
                    const uptime = Date.now() - this.metrics.connectionStartTime;
                    this.metrics.connectionUptime += uptime;
                }
                break;
        }
        
        console.log(`📊 Connection event recorded: ${event.type}`);
    }

    /**
     * Update calculated metrics
     */
    updateMetrics() {
        if (!this.isMonitoring) return;
        
        // Calculate update frequency (updates per minute)
        if (this.metrics.updateIntervals.length > 0) {
            const averageInterval = this.metrics.updateIntervals.reduce((sum, interval) => sum + interval, 0) / this.metrics.updateIntervals.length;
            this.metrics.updateFrequency = Math.round(60000 / averageInterval); // Updates per minute
        }
        
        // Log performance summary
        if (this.metrics.totalUpdates > 0) {
            console.log('📊 Performance Summary:', {
                totalUpdates: this.metrics.totalUpdates,
                updateFrequency: `${this.metrics.updateFrequency}/min`,
                averageLatency: `${Math.round(this.metrics.averageLatency)}ms`,
                reconnections: this.metrics.reconnectionCount
            });
        }
    }

    /**
     * Get current performance metrics
     * @returns {Object} Performance metrics
     */
    getMetrics() {
        return {
            ...this.metrics,
            isMonitoring: this.isMonitoring,
            currentUptime: this.metrics.connectionStartTime ? Date.now() - this.metrics.connectionStartTime : 0
        };
    }

    /**
     * Get performance health status
     * @returns {Object} Health status with recommendations
     */
    getHealthStatus() {
        const metrics = this.getMetrics();
        const health = {
            overall: 'good',
            issues: [],
            recommendations: []
        };
        
        // Check latency
        if (metrics.averageLatency > 5000) {
            health.overall = 'poor';
            health.issues.push('High latency detected');
            health.recommendations.push('Check network connection');
        } else if (metrics.averageLatency > 2000) {
            health.overall = 'fair';
            health.issues.push('Moderate latency detected');
        }
        
        // Check reconnection frequency
        if (metrics.reconnectionCount > 5) {
            health.overall = 'poor';
            health.issues.push('Frequent reconnections detected');
            health.recommendations.push('Check network stability');
        }
        
        // Check update frequency
        if (metrics.updateFrequency < 1) {
            health.overall = 'poor';
            health.issues.push('Very low update frequency');
            health.recommendations.push('Check GPS data source');
        }
        
        return health;
    }

    /**
     * Reset all metrics
     */
    reset() {
        this.metrics = {
            totalUpdates: 0,
            updateFrequency: 0,
            averageLatency: 0,
            connectionUptime: 0,
            reconnectionCount: 0,
            lastUpdateTime: null,
            connectionStartTime: null,
            latencyHistory: [],
            updateIntervals: []
        };
        
        console.log('📊 Performance metrics reset');
    }

    /**
     * Export metrics for analysis
     * @returns {Object} Exportable metrics data
     */
    exportMetrics() {
        return {
            timestamp: new Date().toISOString(),
            metrics: this.getMetrics(),
            health: this.getHealthStatus(),
            historyData: {
                latencyHistory: [...this.metrics.latencyHistory],
                updateIntervals: [...this.metrics.updateIntervals]
            }
        };
    }
}

// Create singleton instance
const realTimePerformanceMonitor = new RealTimePerformanceMonitor();

export default realTimePerformanceMonitor;
