/**
 * Real-Time Tracking Service
 * 
 * Enterprise-grade Server-Sent Events (SSE) client for real-time GPS tracking
 * Provides automatic reconnection, error handling, and connection management
 */

import { API_ENDPOINTS } from '../constants';
import realTimePerformanceMonitor from '../utils/RealTimePerformanceMonitor';

class RealTimeTrackingService {
    constructor() {
        this.eventSource = null;
        this.agentId = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 10;
        this.reconnectDelay = 1000; // Start with 1 second
        this.maxReconnectDelay = 30000; // Max 30 seconds
        this.listeners = new Map();
        this.connectionListeners = new Set();
        this.heartbeatTimeout = null;
        this.lastHeartbeat = null;
        
        // Bind methods to preserve context
        this.handleMessage = this.handleMessage.bind(this);
        this.handleOpen = this.handleOpen.bind(this);
        this.handleError = this.handleError.bind(this);
    }

    /**
     * Connect to real-time tracking stream for an agent
     * @param {string} agentId - Agent ID to track
     * @returns {Promise<void>}
     */
    async connect(agentId) {
        if (this.agentId === agentId && this.isConnected) {
            console.log('🔄 Already connected to agent:', agentId);
            return;
        }

        // Disconnect existing connection
        if (this.eventSource) {
            this.disconnect();
        }

        this.agentId = agentId;
        console.log('🚀 Connecting to real-time tracking for agent:', agentId);

        try {
            const streamUrl = `${API_ENDPOINTS.BASE_URL}/api/tracking/stream/${agentId}`;
            console.log('📡 SSE Stream URL:', streamUrl);

            this.eventSource = new EventSource(streamUrl);
            
            // Set up event listeners
            this.eventSource.onopen = this.handleOpen;
            this.eventSource.onmessage = this.handleMessage;
            this.eventSource.onerror = this.handleError;

            // Set connection timeout
            setTimeout(() => {
                if (!this.isConnected) {
                    console.warn('⚠️ SSE connection timeout');
                    this.handleConnectionFailure();
                }
            }, 10000); // 10 second timeout

        } catch (error) {
            console.error('❌ Failed to create SSE connection:', error);
            this.handleConnectionFailure();
        }
    }

    /**
     * Disconnect from real-time tracking stream
     */
    disconnect() {
        console.log('🔌 Disconnecting from real-time tracking');

        this.isConnected = false;
        this.reconnectAttempts = 0;

        if (this.heartbeatTimeout) {
            clearTimeout(this.heartbeatTimeout);
            this.heartbeatTimeout = null;
        }

        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
        }

        // Stop performance monitoring
        realTimePerformanceMonitor.stopMonitoring();

        // Record disconnection event
        const disconnectionEvent = { type: 'disconnected', agentId: this.agentId };
        realTimePerformanceMonitor.recordConnectionEvent(disconnectionEvent);

        // Notify connection listeners
        this.connectionListeners.forEach(listener => {
            try {
                listener(disconnectionEvent);
            } catch (error) {
                console.error('❌ Error in connection listener:', error);
            }
        });
    }

    /**
     * Add event listener for specific event types
     * @param {string} eventType - Event type to listen for
     * @param {Function} callback - Callback function
     */
    addEventListener(eventType, callback) {
        if (!this.listeners.has(eventType)) {
            this.listeners.set(eventType, new Set());
        }
        this.listeners.get(eventType).add(callback);
        
        console.log(`📝 Added listener for event type: ${eventType}`);
    }

    /**
     * Remove event listener
     * @param {string} eventType - Event type
     * @param {Function} callback - Callback function to remove
     */
    removeEventListener(eventType, callback) {
        if (this.listeners.has(eventType)) {
            this.listeners.get(eventType).delete(callback);
        }
    }

    /**
     * Add connection status listener
     * @param {Function} callback - Callback for connection events
     */
    addConnectionListener(callback) {
        this.connectionListeners.add(callback);
    }

    /**
     * Remove connection status listener
     * @param {Function} callback - Callback to remove
     */
    removeConnectionListener(callback) {
        this.connectionListeners.delete(callback);
    }

    /**
     * Handle SSE connection open
     */
    handleOpen() {
        console.log('✅ SSE connection established');
        this.isConnected = true;
        this.reconnectAttempts = 0;
        this.reconnectDelay = 1000; // Reset delay
        this.lastHeartbeat = Date.now();

        // Start performance monitoring
        realTimePerformanceMonitor.startMonitoring();

        // Record connection event
        const connectionEvent = { type: 'connected', agentId: this.agentId };
        realTimePerformanceMonitor.recordConnectionEvent(connectionEvent);

        // Notify connection listeners
        this.connectionListeners.forEach(listener => {
            try {
                listener(connectionEvent);
            } catch (error) {
                console.error('❌ Error in connection listener:', error);
            }
        });
    }

    /**
     * Handle SSE message
     * @param {MessageEvent} event - SSE message event
     */
    handleMessage(event) {
        try {
            const data = JSON.parse(event.data);
            console.log('📨 Received SSE message:', data.type, data);

            // Handle heartbeat
            if (data.type === 'heartbeat') {
                this.handleHeartbeat();
                return;
            }

            // Handle connection confirmation
            if (data.type === 'connected') {
                console.log('🔗 SSE connection confirmed for agent:', data.agent_id);
                return;
            }

            // Record location updates for performance monitoring
            if (data.type === 'location_update' || data.type === 'current_location') {
                realTimePerformanceMonitor.recordLocationUpdate(data);
            }

            // Dispatch to registered listeners
            if (this.listeners.has(data.type)) {
                this.listeners.get(data.type).forEach(callback => {
                    try {
                        callback(data);
                    } catch (error) {
                        console.error(`❌ Error in ${data.type} listener:`, error);
                    }
                });
            }

            // Also dispatch to 'all' listeners
            if (this.listeners.has('all')) {
                this.listeners.get('all').forEach(callback => {
                    try {
                        callback(data);
                    } catch (error) {
                        console.error('❌ Error in all-events listener:', error);
                    }
                });
            }

        } catch (error) {
            console.error('❌ Failed to parse SSE message:', error, event.data);
        }
    }

    /**
     * Handle SSE connection error
     * @param {Event} error - Error event
     */
    handleError(error) {
        console.error('❌ SSE connection error:', error);
        
        if (this.eventSource && this.eventSource.readyState === EventSource.CLOSED) {
            console.log('🔄 SSE connection closed, attempting reconnection...');
            this.handleConnectionFailure();
        }
    }

    /**
     * Handle heartbeat message
     */
    handleHeartbeat() {
        this.lastHeartbeat = Date.now();
        
        // Reset heartbeat timeout
        if (this.heartbeatTimeout) {
            clearTimeout(this.heartbeatTimeout);
        }
        
        // Set new timeout (45 seconds - 15 seconds buffer)
        this.heartbeatTimeout = setTimeout(() => {
            console.warn('⚠️ Heartbeat timeout - connection may be lost');
            this.handleConnectionFailure();
        }, 45000);
    }

    /**
     * Handle connection failure and attempt reconnection
     */
    handleConnectionFailure() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('❌ Max reconnection attempts reached');
            this.disconnect();
            return;
        }

        this.isConnected = false;
        this.reconnectAttempts++;
        
        console.log(`🔄 Reconnection attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${this.reconnectDelay}ms`);

        // Notify connection listeners
        this.connectionListeners.forEach(listener => {
            try {
                listener({ 
                    type: 'reconnecting', 
                    agentId: this.agentId, 
                    attempt: this.reconnectAttempts,
                    maxAttempts: this.maxReconnectAttempts
                });
            } catch (error) {
                console.error('❌ Error in connection listener:', error);
            }
        });

        setTimeout(() => {
            if (this.agentId) {
                this.connect(this.agentId);
            }
        }, this.reconnectDelay);

        // Exponential backoff with jitter
        this.reconnectDelay = Math.min(
            this.reconnectDelay * 2 + Math.random() * 1000,
            this.maxReconnectDelay
        );
    }

    /**
     * Get connection status
     * @returns {Object} Connection status information
     */
    getConnectionStatus() {
        return {
            isConnected: this.isConnected,
            agentId: this.agentId,
            reconnectAttempts: this.reconnectAttempts,
            lastHeartbeat: this.lastHeartbeat,
            readyState: this.eventSource ? this.eventSource.readyState : null
        };
    }

    /**
     * Check if service is connected
     * @returns {boolean} Connection status
     */
    isConnectedToAgent(agentId) {
        return this.isConnected && this.agentId === agentId;
    }

    /**
     * Get performance metrics
     * @returns {Object} Performance metrics and health status
     */
    getPerformanceMetrics() {
        return {
            metrics: realTimePerformanceMonitor.getMetrics(),
            health: realTimePerformanceMonitor.getHealthStatus(),
            export: realTimePerformanceMonitor.exportMetrics()
        };
    }

    /**
     * Reset performance monitoring
     */
    resetPerformanceMonitoring() {
        realTimePerformanceMonitor.reset();
    }
}

// Create singleton instance
const realTimeTrackingService = new RealTimeTrackingService();

export default realTimeTrackingService;
