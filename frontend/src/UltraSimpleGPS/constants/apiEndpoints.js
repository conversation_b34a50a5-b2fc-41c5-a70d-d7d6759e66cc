/**
 * API Endpoints Configuration
 * 
 * Centralized configuration for all API endpoints used by UltraSimpleGPS
 */

// Base API URL with fallback
export const BASE_API_URL = process.env.REACT_APP_API_URL || '';

// API Endpoints
export const API_ENDPOINTS = {
  // Base URL for building custom endpoints
  BASE_URL: BASE_API_URL,

  // Tracking endpoints
  TRACKING_HISTORY: (agentId) => `${BASE_API_URL}/api/tracking/history/${agentId}`,
  LIVE_LOCATION: (agentId) => `${BASE_API_URL}/api/tracking/live-location/${agentId}`,
  REAL_TIME_STREAM: (agentId) => `${BASE_API_URL}/api/tracking/stream/${agentId}`,

  // Marker endpoints
  MARKER_SETTINGS: (agentId) => `${BASE_API_URL}/api/markers/settings/${agentId}`,

  // Delivery endpoints (using trip endpoints from backend)
  DELIVERIES: `${BASE_API_URL}/api/trip`,
  DELIVERY_BY_DATE: (agentId, date) => `${BASE_API_URL}/api/trip/agent/${agentId}/deliveries?date=${date}`,

  // Routing endpoints (proxy through backend to avoid CORS)
  ROUTING_DIRECTIONS: `${BASE_API_URL}/api/routing/directions`,
  ROUTING_DISTANCE_MATRIX: `${BASE_API_URL}/api/routing/distance-matrix`,
};

// External API endpoints
export const EXTERNAL_APIS = {
  // OLA Maps API
  OLA_MAPS: {
    BASE_URL: 'https://api.olamaps.io',
    GEOCODING: 'https://api.olamaps.io/places/v1/geocode',
    DIRECTIONS: 'https://api.olamaps.io/routing/v1/directions',
    DIRECTIONS_BASIC: 'https://api.olamaps.io/routing/v1/directions-basic',
    DISTANCE_MATRIX: 'https://api.olamaps.io/routing/v1/distanceMatrix',
    DISTANCE_MATRIX_BASIC: 'https://api.olamaps.io/routing/v1/distance-matrix-basic',
  },
  
  // OpenStreetMap Nominatim
  OSM_GEOCODING: 'https://nominatim.openstreetmap.org/search',
  
  // Satellite imagery sources
  SATELLITE_IMAGERY: {
    ESRI_PRIMARY: 'https://services.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
    ESRI_FALLBACK: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
  }
};

// Request headers
export const REQUEST_HEADERS = {
  DEFAULT: {
    'Content-Type': 'application/json',
  },
  
  OLA_MAPS: {
    'Content-Type': 'application/json',
    'X-Request-Id': () => `request-${Date.now()}`,
  }
};
