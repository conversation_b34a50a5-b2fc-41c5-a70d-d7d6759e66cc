/**
 * Real-Time Debug Panel
 * 
 * Enterprise-grade debugging and monitoring panel for real-time GPS tracking
 * Shows connection status, performance metrics, and system health
 */

import React, { useState, useEffect } from 'react';
import realTimeTrackingService from '../services/RealTimeTrackingService';

const RealTimeDebugPanel = ({ isVisible, onClose, agentId }) => {
    const [metrics, setMetrics] = useState(null);
    const [connectionStatus, setConnectionStatus] = useState(null);
    const [refreshInterval, setRefreshInterval] = useState(null);

    // Update metrics every 2 seconds when panel is visible
    useEffect(() => {
        if (!isVisible) {
            if (refreshInterval) {
                clearInterval(refreshInterval);
                setRefreshInterval(null);
            }
            return;
        }

        const updateMetrics = () => {
            try {
                const performanceData = realTimeTrackingService.getPerformanceMetrics();
                const status = realTimeTrackingService.getConnectionStatus();
                
                setMetrics(performanceData);
                setConnectionStatus(status);
            } catch (error) {
                console.error('❌ Error updating debug metrics:', error);
            }
        };

        // Initial update
        updateMetrics();

        // Set up interval
        const interval = setInterval(updateMetrics, 2000);
        setRefreshInterval(interval);

        return () => {
            if (interval) {
                clearInterval(interval);
            }
        };
    }, [isVisible]);

    if (!isVisible) {
        return null;
    }

    const formatUptime = (ms) => {
        if (!ms) return '0s';
        const seconds = Math.floor(ms / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        
        if (hours > 0) return `${hours}h ${minutes % 60}m`;
        if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
        return `${seconds}s`;
    };

    const getHealthColor = (health) => {
        switch (health) {
            case 'good': return '#4CAF50';
            case 'fair': return '#FF9800';
            case 'poor': return '#F44336';
            default: return '#9E9E9E';
        }
    };

    return (
        <div style={{
            position: 'fixed',
            top: '20px',
            right: '20px',
            width: '400px',
            maxHeight: '80vh',
            backgroundColor: 'rgba(0, 0, 0, 0.9)',
            color: 'white',
            borderRadius: '8px',
            padding: '16px',
            zIndex: 10000,
            fontFamily: 'monospace',
            fontSize: '12px',
            overflow: 'auto',
            boxShadow: '0 4px 20px rgba(0,0,0,0.5)'
        }}>
            {/* Header */}
            <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: '16px',
                borderBottom: '1px solid #333',
                paddingBottom: '8px'
            }}>
                <h3 style={{ margin: 0, color: '#00ff00' }}>
                    📊 Real-Time Debug Panel
                </h3>
                <button
                    onClick={onClose}
                    style={{
                        background: 'none',
                        border: 'none',
                        color: 'white',
                        fontSize: '16px',
                        cursor: 'pointer'
                    }}
                >
                    ✕
                </button>
            </div>

            {/* Connection Status */}
            <div style={{ marginBottom: '16px' }}>
                <h4 style={{ color: '#00bfff', margin: '0 0 8px 0' }}>Connection Status</h4>
                {connectionStatus && (
                    <div style={{ backgroundColor: '#1a1a1a', padding: '8px', borderRadius: '4px' }}>
                        <div>Agent ID: {agentId}</div>
                        <div>Connected: {connectionStatus.isConnected ? '✅ Yes' : '❌ No'}</div>
                        <div>Reconnect Attempts: {connectionStatus.reconnectAttempts}</div>
                        <div>Last Heartbeat: {connectionStatus.lastHeartbeat ? 
                            new Date(connectionStatus.lastHeartbeat).toLocaleTimeString() : 'None'}</div>
                        <div>Ready State: {connectionStatus.readyState}</div>
                    </div>
                )}
            </div>

            {/* Performance Metrics */}
            {metrics && (
                <>
                    <div style={{ marginBottom: '16px' }}>
                        <h4 style={{ color: '#00bfff', margin: '0 0 8px 0' }}>Performance Metrics</h4>
                        <div style={{ backgroundColor: '#1a1a1a', padding: '8px', borderRadius: '4px' }}>
                            <div>Total Updates: {metrics.metrics.totalUpdates}</div>
                            <div>Update Frequency: {metrics.metrics.updateFrequency}/min</div>
                            <div>Average Latency: {Math.round(metrics.metrics.averageLatency)}ms</div>
                            <div>Reconnections: {metrics.metrics.reconnectionCount}</div>
                            <div>Uptime: {formatUptime(metrics.metrics.currentUptime)}</div>
                        </div>
                    </div>

                    {/* Health Status */}
                    <div style={{ marginBottom: '16px' }}>
                        <h4 style={{ color: '#00bfff', margin: '0 0 8px 0' }}>System Health</h4>
                        <div style={{ backgroundColor: '#1a1a1a', padding: '8px', borderRadius: '4px' }}>
                            <div style={{ 
                                color: getHealthColor(metrics.health.overall),
                                fontWeight: 'bold',
                                marginBottom: '4px'
                            }}>
                                Overall: {metrics.health.overall.toUpperCase()}
                            </div>
                            
                            {metrics.health.issues.length > 0 && (
                                <div style={{ marginBottom: '8px' }}>
                                    <div style={{ color: '#ff6b6b', fontWeight: 'bold' }}>Issues:</div>
                                    {metrics.health.issues.map((issue, index) => (
                                        <div key={index} style={{ marginLeft: '8px' }}>• {issue}</div>
                                    ))}
                                </div>
                            )}
                            
                            {metrics.health.recommendations.length > 0 && (
                                <div>
                                    <div style={{ color: '#ffd93d', fontWeight: 'bold' }}>Recommendations:</div>
                                    {metrics.health.recommendations.map((rec, index) => (
                                        <div key={index} style={{ marginLeft: '8px' }}>• {rec}</div>
                                    ))}
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Latency Chart (Simple Text Visualization) */}
                    {metrics.metrics.latencyHistory && metrics.metrics.latencyHistory.length > 0 && (
                        <div style={{ marginBottom: '16px' }}>
                            <h4 style={{ color: '#00bfff', margin: '0 0 8px 0' }}>Latency History (Last 10)</h4>
                            <div style={{ backgroundColor: '#1a1a1a', padding: '8px', borderRadius: '4px' }}>
                                {metrics.metrics.latencyHistory.slice(-10).map((latency, index) => (
                                    <div key={index} style={{
                                        display: 'flex',
                                        alignItems: 'center',
                                        marginBottom: '2px'
                                    }}>
                                        <span style={{ width: '20px' }}>{index + 1}:</span>
                                        <div style={{
                                            width: `${Math.min(latency / 50, 100)}px`,
                                            height: '12px',
                                            backgroundColor: latency > 2000 ? '#ff6b6b' : latency > 1000 ? '#ffd93d' : '#4CAF50',
                                            marginRight: '8px'
                                        }} />
                                        <span>{Math.round(latency)}ms</span>
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}
                </>
            )}

            {/* Actions */}
            <div style={{ marginTop: '16px', borderTop: '1px solid #333', paddingTop: '8px' }}>
                <button
                    onClick={() => realTimeTrackingService.resetPerformanceMonitoring()}
                    style={{
                        backgroundColor: '#333',
                        color: 'white',
                        border: '1px solid #555',
                        padding: '4px 8px',
                        borderRadius: '4px',
                        cursor: 'pointer',
                        marginRight: '8px',
                        fontSize: '11px'
                    }}
                >
                    Reset Metrics
                </button>
                <button
                    onClick={() => {
                        const data = realTimeTrackingService.getPerformanceMetrics();
                        console.log('📊 Performance Export:', data.export);
                        alert('Performance data exported to console');
                    }}
                    style={{
                        backgroundColor: '#333',
                        color: 'white',
                        border: '1px solid #555',
                        padding: '4px 8px',
                        borderRadius: '4px',
                        cursor: 'pointer',
                        fontSize: '11px'
                    }}
                >
                    Export Data
                </button>
            </div>
        </div>
    );
};

export default RealTimeDebugPanel;
