package handlers

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"

	"delivery-tracking-backend/models"
	"delivery-tracking-backend/repository"
	"delivery-tracking-backend/utils"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// SSEClient represents a Server-Sent Events client connection
type SSEClient struct {
	AgentID    uuid.UUID
	Channel    chan []byte
	Context    context.Context
	CancelFunc context.CancelFunc
}

// SSEManager manages all SSE connections for real-time tracking
type SSEManager struct {
	clients map[uuid.UUID]map[string]*SSEClient // agentID -> clientID -> client
	mutex   sync.RWMutex
}

// Global SSE manager instance
var sseManager = &SSEManager{
	clients: make(map[uuid.UUID]map[string]*SSEClient),
}

type TrackingHandler struct {
	trackingRepo *repository.TrackingRepository
}

// SaveGeocodedNameRequest represents a request to save a geocoded name
type SaveGeocodedNameRequest struct {
	EventID      string `json:"event_id" validate:"required"`
	GeocodedName string `json:"geocoded_name" validate:"required"`
}

// AddClient adds a new SSE client for an agent
func (sm *SSEManager) AddClient(agentID uuid.UUID, clientID string, client *SSEClient) {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	if sm.clients[agentID] == nil {
		sm.clients[agentID] = make(map[string]*SSEClient)
	}
	sm.clients[agentID][clientID] = client

	logrus.WithFields(logrus.Fields{
		"agent_id":  agentID,
		"client_id": clientID,
	}).Info("SSE client connected")
}

// RemoveClient removes an SSE client
func (sm *SSEManager) RemoveClient(agentID uuid.UUID, clientID string) {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	if clients, exists := sm.clients[agentID]; exists {
		if client, exists := clients[clientID]; exists {
			client.CancelFunc()
			close(client.Channel)
			delete(clients, clientID)

			// Clean up empty agent map
			if len(clients) == 0 {
				delete(sm.clients, agentID)
			}
		}
	}

	logrus.WithFields(logrus.Fields{
		"agent_id":  agentID,
		"client_id": clientID,
	}).Info("SSE client disconnected")
}

// BroadcastToAgent sends data to all clients watching a specific agent
func (sm *SSEManager) BroadcastToAgent(agentID uuid.UUID, data []byte) {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()

	if clients, exists := sm.clients[agentID]; exists {
		for clientID, client := range clients {
			select {
			case client.Channel <- data:
				// Successfully sent
			default:
				// Channel is full or closed, remove client
				logrus.WithFields(logrus.Fields{
					"agent_id":  agentID,
					"client_id": clientID,
				}).Warn("SSE client channel full, removing client")
				go sm.RemoveClient(agentID, clientID)
			}
		}
	}
}

func NewTrackingHandler(trackingRepo *repository.TrackingRepository) *TrackingHandler {
	return &TrackingHandler{
		trackingRepo: trackingRepo,
	}
}

// LocationPing handles GPS location pings from agents
// POST /api/location/ping
func (h *TrackingHandler) LocationPing(c *gin.Context) {
	var req models.LocationPingRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logrus.WithError(err).Error("Invalid location ping request")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid request format", err.Error()))
		return
	}

	// Validate the request
	if err := utils.ValidateStruct(&req); err != nil {
		logrus.WithError(err).Error("Location ping validation failed")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Validation failed", err.Error()))
		return
	}

	// Process the location ping
	if err := h.trackingRepo.ProcessLocationPing(&req); err != nil {
		logrus.WithError(err).Error("Failed to process location ping")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to process location ping", err.Error()))
		return
	}

	logrus.WithFields(logrus.Fields{
		"agent_id": req.AgentID,
		"location": req.Location,
		"timestamp": req.Timestamp,
	}).Info("Location ping processed successfully")

	c.JSON(http.StatusOK, utils.SuccessResponse("Location ping processed successfully", nil))

	// Broadcast real-time update to SSE clients
	go h.broadcastLocationUpdate(req.AgentID, &req)
}

// broadcastLocationUpdate sends real-time location updates to SSE clients
func (h *TrackingHandler) broadcastLocationUpdate(agentID uuid.UUID, locationData *models.LocationPingRequest) {
	// Create real-time update payload
	updatePayload := map[string]interface{}{
		"type":      "location_update",
		"agent_id":  agentID,
		"timestamp": locationData.Timestamp,
		"location": map[string]interface{}{
			"latitude":  locationData.Location.Latitude,
			"longitude": locationData.Location.Longitude,
		},
		"speed_m_s":    locationData.SpeedMS,
		"heading_deg":  locationData.HeadingDeg,
		"accuracy_m":   locationData.AccuracyM,
		"battery_pct":  locationData.BatteryPct,
	}

	// Convert to JSON
	jsonData, err := json.Marshal(updatePayload)
	if err != nil {
		logrus.WithError(err).Error("Failed to marshal location update for SSE")
		return
	}

	// Format as SSE message
	sseMessage := fmt.Sprintf("data: %s\n\n", string(jsonData))

	// Broadcast to all clients watching this agent
	sseManager.BroadcastToAgent(agentID, []byte(sseMessage))

	logrus.WithFields(logrus.Fields{
		"agent_id": agentID,
		"lat":      locationData.Location.Latitude,
		"lng":      locationData.Location.Longitude,
	}).Debug("Broadcasted real-time location update")
}

// StreamLiveLocation provides Server-Sent Events stream for real-time location updates
// GET /api/tracking/stream/{agent_id}
func (h *TrackingHandler) StreamLiveLocation(c *gin.Context) {
	agentIDStr := c.Param("agent_id")
	agentID, err := uuid.Parse(agentIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid agent ID", "Agent ID must be a valid UUID"))
		return
	}

	// Generate unique client ID
	clientID := uuid.New().String()

	// Set SSE headers
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Access-Control-Allow-Origin", "*")
	c.Header("Access-Control-Allow-Headers", "Cache-Control")

	// Create client context with cancellation
	ctx, cancel := context.WithCancel(c.Request.Context())

	// Create SSE client
	client := &SSEClient{
		AgentID:    agentID,
		Channel:    make(chan []byte, 100), // Buffer for 100 messages
		Context:    ctx,
		CancelFunc: cancel,
	}

	// Add client to manager
	sseManager.AddClient(agentID, clientID, client)

	// Clean up on disconnect
	defer func() {
		sseManager.RemoveClient(agentID, clientID)
		logrus.WithFields(logrus.Fields{
			"agent_id":  agentID,
			"client_id": clientID,
		}).Info("SSE connection closed")
	}()

	// Send initial connection confirmation
	initialMessage := fmt.Sprintf("data: {\"type\":\"connected\",\"agent_id\":\"%s\",\"client_id\":\"%s\"}\n\n", agentID, clientID)
	c.Writer.WriteString(initialMessage)
	c.Writer.Flush()

	// Send current location if available
	go h.sendCurrentLocation(agentID, client)

	// Keep connection alive and send updates
	ticker := time.NewTicker(30 * time.Second) // Heartbeat every 30 seconds
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			// Client disconnected
			return
		case message := <-client.Channel:
			// Send location update
			_, err := c.Writer.Write(message)
			if err != nil {
				logrus.WithError(err).Error("Failed to write SSE message")
				return
			}
			c.Writer.Flush()
		case <-ticker.C:
			// Send heartbeat
			heartbeat := "data: {\"type\":\"heartbeat\"}\n\n"
			_, err := c.Writer.WriteString(heartbeat)
			if err != nil {
				logrus.WithError(err).Error("Failed to send heartbeat")
				return
			}
			c.Writer.Flush()
		}
	}
}

// sendCurrentLocation sends the current location to a newly connected client
func (h *TrackingHandler) sendCurrentLocation(agentID uuid.UUID, client *SSEClient) {
	currentLocation, err := h.trackingRepo.GetLiveLocation(agentID)
	if err != nil {
		logrus.WithError(err).Debug("No current location available for agent")
		return
	}

	// Create current location payload
	locationPayload := map[string]interface{}{
		"type":      "current_location",
		"agent_id":  agentID,
		"timestamp": currentLocation.Timestamp,
		"location": map[string]interface{}{
			"latitude":  currentLocation.Geom.Latitude,
			"longitude": currentLocation.Geom.Longitude,
		},
		"speed_m_s":   currentLocation.SpeedMS,
		"heading_deg": currentLocation.HeadingDeg,
		"accuracy_m":  currentLocation.AccuracyM,
		"battery_pct": currentLocation.BatteryPct,
	}

	// Convert to JSON and send
	jsonData, err := json.Marshal(locationPayload)
	if err != nil {
		logrus.WithError(err).Error("Failed to marshal current location")
		return
	}

	sseMessage := fmt.Sprintf("data: %s\n\n", string(jsonData))

	select {
	case client.Channel <- []byte(sseMessage):
		logrus.WithField("agent_id", agentID).Debug("Sent current location to new SSE client")
	default:
		logrus.WithField("agent_id", agentID).Warn("Failed to send current location - client channel full")
	}
}

// GetLiveLocation retrieves current live location of an agent
// GET /api/live-location/:agent_id
func (h *TrackingHandler) GetLiveLocation(c *gin.Context) {
	agentIDStr := c.Param("agent_id")
	agentID, err := uuid.Parse(agentIDStr)
	if err != nil {
		logrus.WithError(err).Error("Invalid agent ID format")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid agent ID format", err.Error()))
		return
	}

	location, err := h.trackingRepo.GetLiveLocation(agentID)
	if err != nil {
		if err.Error() == "live location not found for agent" {
			c.JSON(http.StatusNotFound, utils.ErrorResponse("Live location not found", err.Error()))
			return
		}
		logrus.WithError(err).Error("Failed to get live location")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get live location", err.Error()))
		return
	}

	c.JSON(http.StatusOK, utils.SuccessResponse("Live location retrieved successfully", location))
}

// GetTrackingHistory retrieves tracking history for an agent
// GET /api/tracking-history/:agent_id
func (h *TrackingHandler) GetTrackingHistory(c *gin.Context) {
	agentIDStr := c.Param("agent_id")
	agentID, err := uuid.Parse(agentIDStr)
	if err != nil {
		logrus.WithError(err).Error("Invalid agent ID format")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid agent ID format", err.Error()))
		return
	}

	// Parse query parameters for date range
	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")
	dateStr := c.Query("date")

	var startDate, endDate time.Time

	if dateStr != "" {
		// Handle special date keywords
		now := time.Now()
		var date time.Time

		switch strings.ToLower(dateStr) {
		case "today":
			date = now
		case "yesterday":
			date = now.AddDate(0, 0, -1)
		default:
			// Try to parse as YYYY-MM-DD format
			var err error
			date, err = time.Parse("2006-01-02", dateStr)
			if err != nil {
				c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid date format", "Use YYYY-MM-DD format, 'today', or 'yesterday'"))
				return
			}
		}

		startDate = time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, date.Location())
		endDate = startDate.Add(24 * time.Hour)
	} else if startDateStr != "" && endDateStr != "" {
		// If date range is provided
		startDate, err = time.Parse("2006-01-02", startDateStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid start_date format", "Use YYYY-MM-DD format"))
			return
		}
		endDate, err = time.Parse("2006-01-02", endDateStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid end_date format", "Use YYYY-MM-DD format"))
			return
		}
		endDate = endDate.Add(24 * time.Hour) // Include the entire end date
	} else {
		// Default to today if no date parameters provided
		now := time.Now()
		startDate = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
		endDate = startDate.Add(24 * time.Hour)
	}

	trackingHistory, err := h.trackingRepo.GetTrackingHistory(agentID, startDate, endDate)
	if err != nil {
		logrus.WithError(err).Error("Failed to get tracking history")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get tracking history", err.Error()))
		return
	}

	// Get agent profile information including name
	agentProfile, err := h.trackingRepo.GetAgentProfileWithName(agentID)
	if err != nil {
		logrus.WithError(err).Warn("Failed to get agent profile, continuing without it")
		agentProfile = nil
	}

	// Get trip status updates for the same date range
	statusUpdates, err := h.trackingRepo.GetTripStatusUpdates(agentID, startDate, endDate)
	if err != nil {
		logrus.WithError(err).Error("Failed to get trip status updates")
		// Don't fail the request, just log the error and continue without status updates
		statusUpdates = []*models.TripStatusUpdate{}
	}

	// Convert trip status updates to timeline format expected by frontend
	timeline := convertStatusUpdatesToTimeline(statusUpdates)

	response := map[string]interface{}{
		"agent_id":       agentID,
		"start_date":     startDate.Format("2006-01-02"),
		"end_date":       endDate.Format("2006-01-02"),
		"total_points":   len(trackingHistory),
		"tracking_data":  trackingHistory,
		"status_updates": statusUpdates,
		"timeline":       timeline, // Add timeline data for frontend
		"agent_profile":  agentProfile, // Add agent profile with name
	}

	c.JSON(http.StatusOK, utils.SuccessResponse("Tracking history retrieved successfully", response))
}

// GetAgentDeliveryStats retrieves delivery statistics for an agent on a specific date
// GET /api/tracking/delivery-stats/:agent_id?date=YYYY-MM-DD
func (h *TrackingHandler) GetAgentDeliveryStats(c *gin.Context) {
	agentIDStr := c.Param("agent_id")
	agentID, err := uuid.Parse(agentIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid agent ID", "Agent ID must be a valid UUID"))
		return
	}

	dateParam := c.DefaultQuery("date", "today")

	// Parse date parameter
	var targetDate time.Time
	if dateParam == "today" {
		targetDate = time.Now()
	} else if dateParam == "yesterday" {
		targetDate = time.Now().AddDate(0, 0, -1)
	} else {
		parsedDate, err := time.Parse("2006-01-02", dateParam)
		if err != nil {
			c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid date format", "Date must be in YYYY-MM-DD format, 'today', or 'yesterday'"))
			return
		}
		targetDate = parsedDate
	}

	deliveryStats, err := h.trackingRepo.GetAgentDeliveryStats(agentID, targetDate)
	if err != nil {
		logrus.WithError(err).Error("Failed to get agent delivery stats")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get delivery stats", err.Error()))
		return
	}

	response := map[string]interface{}{
		"agent_id": agentID,
		"date":     targetDate.Format("2006-01-02"),
		"stats":    deliveryStats,
	}

	c.JSON(http.StatusOK, utils.SuccessResponse("Delivery stats retrieved successfully", response))
}

// GetAgentsDeliveryCounts retrieves delivery counts for all agents
// GET /api/tracking/agents-delivery-counts
func (h *TrackingHandler) GetAgentsDeliveryCounts(c *gin.Context) {
	deliveryCounts, err := h.trackingRepo.GetAgentsDeliveryCounts()
	if err != nil {
		logrus.WithError(err).Error("Failed to get agents delivery counts")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get agents delivery counts", err.Error()))
		return
	}

	response := map[string]interface{}{
		"delivery_counts": deliveryCounts,
	}

	c.JSON(http.StatusOK, utils.SuccessResponse("Agents delivery counts retrieved successfully", response))
}

// convertStatusUpdatesToTimeline converts trip status updates to timeline format expected by frontend
func convertStatusUpdatesToTimeline(statusUpdates []*models.TripStatusUpdate) []map[string]interface{} {
	timeline := make([]map[string]interface{}, 0, len(statusUpdates))

	for _, update := range statusUpdates {
		timelineEvent := map[string]interface{}{
			"id":          update.ID.String(), // Add ID for geocoding save functionality
			"recorded_at": update.Timestamp,
			"longitude":   0.0,
			"latitude":    0.0,
			"address":     "",
			"note":        "",
		}

		// Set location if available
		if update.Location != nil {
			timelineEvent["longitude"] = update.Location.Longitude
			timelineEvent["latitude"] = update.Location.Latitude
		}

		// Set note if available
		if update.Note != nil {
			timelineEvent["note"] = *update.Note
		}

		// Use cached geocoded name if available
		if update.GeocodedName != nil && *update.GeocodedName != "" {
			timelineEvent["address"] = *update.GeocodedName
		}

		// Set boolean flags based on status
		status := string(update.Status)
		timelineEvent["is_checked_in"] = status == "requested" || status == "accepted"
		timelineEvent["is_checked_out"] = status == "cancelled" || status == "returned"
		timelineEvent["has_left_hub"] = status == "picked_up"
		timelineEvent["has_returned_to_hub"] = status == "returned"
		timelineEvent["is_delivered"] = status == "delivered"
		timelineEvent["is_failed"] = status == "failed"
		timelineEvent["is_rescheduled"] = status == "rescheduled"
		timelineEvent["is_waiting"] = status == "in_transit" || status == "out_for_delivery"

		// Add event_type field for frontend compatibility
		eventTypeMap := map[string]string{
			"requested":        "checked_in",
			"accepted":         "checked_in",
			"picked_up":        "checked_out",
			"in_transit":       "break_start",
			"out_for_delivery": "break_end",
			"delivered":        "delivered",
			"failed":           "failed",
			"rescheduled":      "rescheduled",
			"cancelled":        "checked_out",
			"returned":         "checked_out",
		}

		if eventType, exists := eventTypeMap[status]; exists {
			timelineEvent["event_type"] = eventType
		} else {
			timelineEvent["event_type"] = status
		}

		timeline = append(timeline, timelineEvent)
	}

	return timeline
}

// GetActiveAgents retrieves all agents with recent location updates
// GET /api/active-agents
func (h *TrackingHandler) GetActiveAgents(c *gin.Context) {
	// Parse query parameter for time window (default: 15 minutes)
	withinMinutesStr := c.DefaultQuery("within_minutes", "15")
	withinMinutes, err := strconv.Atoi(withinMinutesStr)
	if err != nil || withinMinutes <= 0 {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid within_minutes parameter", "Must be a positive integer"))
		return
	}

	activeAgents, err := h.trackingRepo.GetActiveAgents(withinMinutes)
	if err != nil {
		logrus.WithError(err).Error("Failed to get active agents")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get active agents", err.Error()))
		return
	}

	// Ensure agents is never nil - always return an empty array if no agents found
	if activeAgents == nil {
		activeAgents = []*models.LiveLocation{}
	}

	response := map[string]interface{}{
		"within_minutes": withinMinutes,
		"total_agents":   len(activeAgents),
		"agents":         activeAgents,
	}

	c.JSON(http.StatusOK, utils.SuccessResponse("Active agents retrieved successfully", response))
}

// BatchLocationPing handles multiple GPS location pings at once
// POST /api/location/batch-ping
func (h *TrackingHandler) BatchLocationPing(c *gin.Context) {
	var requests []models.LocationPingRequest
	if err := c.ShouldBindJSON(&requests); err != nil {
		logrus.WithError(err).Error("Invalid batch location ping request")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid request format", err.Error()))
		return
	}

	if len(requests) == 0 {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Empty batch request", "At least one location ping is required"))
		return
	}

	if len(requests) > 100 {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Batch too large", "Maximum 100 location pings per batch"))
		return
	}

	successCount := 0
	errors := []string{}

	for i, req := range requests {
		// Validate each request
		if err := utils.ValidateStruct(&req); err != nil {
			errors = append(errors, fmt.Sprintf("Request %d: %s", i+1, err.Error()))
			continue
		}

		// Process the location ping
		if err := h.trackingRepo.ProcessLocationPing(&req); err != nil {
			errors = append(errors, fmt.Sprintf("Request %d: %s", i+1, err.Error()))
			continue
		}

		successCount++
	}

	response := map[string]interface{}{
		"total_requests":     len(requests),
		"successful_pings":   successCount,
		"failed_pings":       len(errors),
		"errors":             errors,
	}

	if len(errors) > 0 {
		logrus.WithFields(logrus.Fields{
			"total_requests":   len(requests),
			"successful_pings": successCount,
			"failed_pings":     len(errors),
		}).Warn("Batch location ping completed with errors")
		
		c.JSON(http.StatusPartialContent, utils.SuccessResponse("Batch location ping completed with errors", response))
	} else {
		logrus.WithFields(logrus.Fields{
			"total_requests":   len(requests),
			"successful_pings": successCount,
		}).Info("Batch location ping completed successfully")
		
		c.JSON(http.StatusOK, utils.SuccessResponse("Batch location ping completed successfully", response))
	}
}

// GetTrackingStats retrieves tracking statistics for an agent
// GET /api/tracking-stats/:agent_id
func (h *TrackingHandler) GetTrackingStats(c *gin.Context) {
	agentIDStr := c.Param("agent_id")
	agentID, err := uuid.Parse(agentIDStr)
	if err != nil {
		logrus.WithError(err).Error("Invalid agent ID format")
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid agent ID format", err.Error()))
		return
	}

	// Parse date parameter (default to today)
	dateStr := c.DefaultQuery("date", time.Now().Format("2006-01-02"))
	date, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, utils.ErrorResponse("Invalid date format", "Use YYYY-MM-DD format"))
		return
	}

	// Get tracking data for the date
	trackingHistory, err := h.trackingRepo.GetTrackingHistoryByDate(agentID, date)
	if err != nil {
		logrus.WithError(err).Error("Failed to get tracking history for stats")
		c.JSON(http.StatusInternalServerError, utils.ErrorResponse("Failed to get tracking stats", err.Error()))
		return
	}

	// Calculate statistics
	stats := calculateTrackingStats(trackingHistory, date)
	stats["agent_id"] = agentID
	stats["date"] = dateStr

	c.JSON(http.StatusOK, utils.SuccessResponse("Tracking stats retrieved successfully", stats))
}

// Helper function to calculate tracking statistics
func calculateTrackingStats(trackingData []*models.TrackingUpdate, date time.Time) map[string]interface{} {
	if len(trackingData) == 0 {
		return map[string]interface{}{
			"total_points":     0,
			"distance_km":      0,
			"duration_hours":   0,
			"avg_speed_kmh":    0,
			"max_speed_kmh":    0,
			"battery_start":    nil,
			"battery_end":      nil,
			"battery_min":      nil,
			"battery_max":      nil,
			"first_ping":       nil,
			"last_ping":        nil,
		}
	}

	totalPoints := len(trackingData)
	var totalDistance float64
	var maxSpeed float64
	var batteryStart, batteryEnd, batteryMin, batteryMax *float64

	firstPing := trackingData[0].Timestamp
	lastPing := trackingData[totalPoints-1].Timestamp
	duration := lastPing.Sub(firstPing)

	// Calculate distance and speed statistics
	for i := 1; i < len(trackingData); i++ {
		prev := trackingData[i-1]
		curr := trackingData[i]

		// Calculate distance between consecutive points (simplified Haversine)
		distance := utils.CalculateDistance(
			prev.Geom.Latitude, prev.Geom.Longitude,
			curr.Geom.Latitude, curr.Geom.Longitude,
		)
		totalDistance += distance

		// Track max speed
		if curr.SpeedMS != nil && *curr.SpeedMS > maxSpeed {
			maxSpeed = *curr.SpeedMS
		}
	}

	// Battery statistics
	for i, point := range trackingData {
		if point.BatteryPct != nil {
			if i == 0 {
				batteryStart = point.BatteryPct
			}
			if i == len(trackingData)-1 {
				batteryEnd = point.BatteryPct
			}
			if batteryMin == nil || *point.BatteryPct < *batteryMin {
				batteryMin = point.BatteryPct
			}
			if batteryMax == nil || *point.BatteryPct > *batteryMax {
				batteryMax = point.BatteryPct
			}
		}
	}

	avgSpeed := 0.0
	if duration.Hours() > 0 {
		avgSpeed = totalDistance / duration.Hours()
	}

	return map[string]interface{}{
		"total_points":     totalPoints,
		"distance_km":      totalDistance,
		"duration_hours":   duration.Hours(),
		"avg_speed_kmh":    avgSpeed,
		"max_speed_kmh":    maxSpeed * 3.6, // Convert m/s to km/h
		"battery_start":    batteryStart,
		"battery_end":      batteryEnd,
		"battery_min":      batteryMin,
		"battery_max":      batteryMax,
		"first_ping":       firstPing,
		"last_ping":        lastPing,
	}
}

// SaveGeocodedName saves a geocoded location name to the database
func (h *TrackingHandler) SaveGeocodedName(c *gin.Context) {
	var req SaveGeocodedNameRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logrus.WithError(err).Error("Invalid request body for save geocoded name")
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid request body",
		})
		return
	}

	// Validate event ID is a valid UUID
	eventUUID, err := uuid.Parse(req.EventID)
	if err != nil {
		logrus.WithError(err).Error("Invalid event ID format")
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid event ID format",
		})
		return
	}

	// Update the geocoded_name in the database
	err = h.trackingRepo.UpdateGeocodedName(eventUUID, req.GeocodedName)
	if err != nil {
		logrus.WithError(err).Error("Failed to save geocoded name")
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to save geocoded name",
		})
		return
	}

	logrus.WithFields(logrus.Fields{
		"event_id":      req.EventID,
		"geocoded_name": req.GeocodedName,
	}).Info("Geocoded name saved successfully")

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Geocoded name saved successfully",
	})
}
