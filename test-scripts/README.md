# 🎯 GPS Simulator - Real-time Tracking Test Scripts

This directory contains realistic GPS simulation scripts to test your real-time tracking system. The simulator creates natural movement patterns that mimic real human behavior - no random teleportation!

## 🚀 Quick Start

### Prerequisites
- Node.js installed on your system
- Your backend server running on `http://localhost:8080`
- Your frontend open and viewing "today's" tracking for the test agent

### Easy Setup (Recommended)

**For Linux/Mac:**
```bash
cd test-scripts
chmod +x run-simulation.sh
./run-simulation.sh
```

**For Windows:**
```cmd
cd test-scripts
run-simulation.bat
```

### Manual Setup
```bash
cd test-scripts
npm install
node gps-simulator.js WALKING 30
```

## 🎮 Movement Patterns

The simulator includes 4 realistic movement patterns:

### 🚶 WALKING
- **Speed**: 1.5 m/s (normal walking pace)
- **Behavior**: Natural turns and direction changes
- **Use case**: Person walking around, delivery on foot
- **Example**: `node gps-simulator.js WALKING 30`

### 🚴 CYCLING  
- **Speed**: 4.0 m/s (casual cycling)
- **Behavior**: Gentle turns, more consistent direction
- **Use case**: Bicycle delivery, casual cycling
- **Example**: `node gps-simulator.js CYCLING 45`

### 🚗 DRIVING
- **Speed**: 8.0 m/s (city driving ~29 km/h)
- **Behavior**: Road-like movement, minimal sharp turns
- **Use case**: Car delivery, taxi service
- **Example**: `node gps-simulator.js DRIVING 60`

### 📦 DELIVERY
- **Speed**: 2.5 m/s (brisk walking with stops)
- **Behavior**: Frequent direction changes, stop-and-go pattern
- **Use case**: Delivery agent making multiple stops
- **Example**: `node gps-simulator.js DELIVERY 40`

## 🛠️ Configuration

### Environment Variables
```bash
export API_URL=http://localhost:8080    # Your backend URL
```

### Script Configuration
Edit `gps-simulator.js` to customize:

```javascript
const CONFIG = {
    AGENT_ID: '3b35ff6d-d482-4cb5-bbc1-ed78774c59b2', // Change this to test different agents
    DURATION_SECONDS: 30,                              // How long to run
    UPDATE_INTERVAL_MS: 2000,                         // GPS update frequency
    START_LOCATION: {                                  // Starting coordinates
        latitude: 12.9716,   // Bangalore, India
        longitude: 77.5946
    }
};
```

## 📊 What the Simulator Does

### Realistic Movement
- ✅ **Natural distances**: Each GPS point is ~15-30 meters from the previous
- ✅ **Realistic speeds**: Based on actual human/vehicle movement
- ✅ **Smooth direction changes**: No sudden teleportation
- ✅ **Accurate timing**: Consistent update intervals

### GPS Data Generated
```json
{
    "agent_id": "3b35ff6d-d482-4cb5-bbc1-ed78774c59b2",
    "role": "agent",
    "location": {
        "latitude": 12.971634,
        "longitude": 77.594587
    },
    "timestamp": "2024-01-15T10:30:45.123Z",
    "speed_m_s": 1.5,
    "heading_deg": 45.2,
    "accuracy_m": 8.5,
    "altitude_m": 845.2,
    "battery_pct": 85,
    "source": "simulation"
}
```

## 🧪 Testing Scenarios

### Basic Real-time Test
```bash
# 1. Start your backend server
# 2. Open frontend and view today's tracking for the agent
# 3. Run simulation
node gps-simulator.js WALKING 30

# You should see:
# - Real-time GPS line extending on the map
# - Green pulsing marker moving
# - Connection status showing "Live"
```

### Performance Test
```bash
# Test with faster updates
node gps-simulator.js DRIVING 120  # 2 minutes of driving simulation
```

### Connection Recovery Test
```bash
# 1. Start simulation
node gps-simulator.js DELIVERY 60

# 2. During simulation, stop your backend server
# 3. Restart backend server
# 4. Watch frontend reconnect automatically
```

## 🐛 Debugging

### Enable Debug Panel
1. Open your frontend
2. Press `Ctrl+Shift+D` to open debug panel
3. Run simulation and watch real-time metrics

### Common Issues

**"Connection refused"**
- Make sure your backend is running on `http://localhost:8080`
- Check if the API endpoint `/api/location/ping` is accessible

**"No real-time updates in frontend"**
- Ensure you're viewing "today's" tracking (not historical dates)
- Check that real-time toggle is enabled
- Verify agent ID matches in both simulator and frontend

**"Updates too fast/slow"**
- Adjust `UPDATE_INTERVAL_MS` in the config
- Try different movement patterns

## 📈 Expected Results

When working correctly, you should see:

1. **Console Output**: GPS coordinates being sent every 2 seconds
2. **Frontend Map**: Blue GPS line extending in real-time
3. **Live Marker**: Green pulsing dot moving along the route
4. **Connection Status**: "Live" indicator in the UI
5. **Debug Panel**: Real-time metrics and performance data

## 🎯 Pro Tips

1. **Start with WALKING**: It's the easiest to follow visually
2. **Use Debug Panel**: Press `Ctrl+Shift+D` for detailed monitoring
3. **Test Different Patterns**: Each pattern tests different aspects
4. **Monitor Performance**: Check latency and update frequency
5. **Test Reconnection**: Stop/start backend during simulation

## 🔧 Customization

### Add New Movement Pattern
```javascript
CUSTOM_PATTERN: {
    speed: 3.0,           // m/s
    directionChange: 25,  // max degrees
    description: 'Custom movement pattern'
}
```

### Change Starting Location
```javascript
START_LOCATION: {
    latitude: 40.7128,    // New York
    longitude: -74.0060
}
```

### Adjust Realism
- **More realistic**: Increase `UPDATE_INTERVAL_MS` to 5000 (5 seconds)
- **Faster testing**: Decrease to 1000 (1 second)
- **Longer routes**: Increase `DURATION_SECONDS`

---

## 🎉 Happy Testing!

This simulator will help you verify that your real-time GPS tracking system works exactly like professional delivery and ride-sharing apps. The movement patterns are designed to be realistic and easy to follow on the map.

If you see the GPS line extending smoothly and the green marker moving in real-time, congratulations! Your enterprise-grade real-time tracking system is working perfectly! 🚀
