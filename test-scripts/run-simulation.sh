#!/bin/bash

# GPS Simulator Runner Script
# Makes it easy to run GPS simulations for testing

echo "🎯 GPS Simulator - Real-time Tracking Test"
echo "=========================================="

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "gps-simulator.js" ]; then
    echo "❌ gps-simulator.js not found. Please run this script from the test-scripts directory."
    exit 1
fi

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

# Show menu
echo ""
echo "Choose a simulation pattern:"
echo "1) 🚶 WALKING  - Natural walking pace (30 seconds)"
echo "2) 🚴 CYCLING  - Cycling speed (45 seconds)"
echo "3) 🚗 DRIVING  - City driving (60 seconds)"
echo "4) 📦 DELIVERY - Delivery agent with stops (40 seconds)"
echo "5) ❓ HELP     - Show detailed help"
echo "6) 🛠️ CUSTOM   - Enter custom parameters"
echo ""

read -p "Enter your choice (1-6): " choice

case $choice in
    1)
        echo "🚶 Starting WALKING simulation..."
        node gps-simulator.js WALKING 30
        ;;
    2)
        echo "🚴 Starting CYCLING simulation..."
        node gps-simulator.js CYCLING 45
        ;;
    3)
        echo "🚗 Starting DRIVING simulation..."
        node gps-simulator.js DRIVING 60
        ;;
    4)
        echo "📦 Starting DELIVERY simulation..."
        node gps-simulator.js DELIVERY 40
        ;;
    5)
        node gps-simulator.js --help
        ;;
    6)
        echo ""
        echo "Available patterns: WALKING, CYCLING, DRIVING, DELIVERY"
        read -p "Enter pattern: " pattern
        read -p "Enter duration (seconds): " duration
        echo "🎯 Starting $pattern simulation for $duration seconds..."
        node gps-simulator.js "$pattern" "$duration"
        ;;
    *)
        echo "❌ Invalid choice. Please run the script again."
        exit 1
        ;;
esac

echo ""
echo "✅ Simulation completed! Check your frontend for real-time updates."
