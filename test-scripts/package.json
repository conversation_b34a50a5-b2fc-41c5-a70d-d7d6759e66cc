{"name": "gps-simulator", "version": "1.0.0", "description": "Realistic GPS simulation script for testing real-time tracking", "main": "gps-simulator.js", "scripts": {"start": "node gps-simulator.js", "walking": "node gps-simulator.js WALKING 30", "cycling": "node gps-simulator.js CYCLING 45", "driving": "node gps-simulator.js DRIVING 60", "delivery": "node gps-simulator.js DELIVERY 40", "help": "node gps-simulator.js --help"}, "dependencies": {"axios": "^1.6.0"}, "keywords": ["gps", "simulation", "testing", "real-time", "tracking"], "author": "Your Name", "license": "MIT"}