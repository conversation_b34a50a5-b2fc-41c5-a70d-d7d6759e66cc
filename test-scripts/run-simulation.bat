@echo off
title GPS Simulator - Real-time Tracking Test

echo 🎯 GPS Simulator - Real-time Tracking Test
echo ==========================================

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js is not installed. Please install Node.js first.
    pause
    exit /b 1
)

REM Check if we're in the right directory
if not exist "gps-simulator.js" (
    echo ❌ gps-simulator.js not found. Please run this script from the test-scripts directory.
    pause
    exit /b 1
)

REM Install dependencies if needed
if not exist "node_modules" (
    echo 📦 Installing dependencies...
    npm install
)

echo.
echo Choose a simulation pattern:
echo 1^) 🚶 WALKING  - Natural walking pace ^(30 seconds^)
echo 2^) 🚴 CYCLING  - Cycling speed ^(45 seconds^)
echo 3^) 🚗 DRIVING  - City driving ^(60 seconds^)
echo 4^) 📦 DELIVERY - Delivery agent with stops ^(40 seconds^)
echo 5^) ❓ HELP     - Show detailed help
echo 6^) 🛠️ CUSTOM   - Enter custom parameters
echo.

set /p choice="Enter your choice (1-6): "

if "%choice%"=="1" (
    echo 🚶 Starting WALKING simulation...
    node gps-simulator.js WALKING 30
) else if "%choice%"=="2" (
    echo 🚴 Starting CYCLING simulation...
    node gps-simulator.js CYCLING 45
) else if "%choice%"=="3" (
    echo 🚗 Starting DRIVING simulation...
    node gps-simulator.js DRIVING 60
) else if "%choice%"=="4" (
    echo 📦 Starting DELIVERY simulation...
    node gps-simulator.js DELIVERY 40
) else if "%choice%"=="5" (
    node gps-simulator.js --help
) else if "%choice%"=="6" (
    echo.
    echo Available patterns: WALKING, CYCLING, DRIVING, DELIVERY
    set /p pattern="Enter pattern: "
    set /p duration="Enter duration (seconds): "
    echo 🎯 Starting %pattern% simulation for %duration% seconds...
    node gps-simulator.js "%pattern%" "%duration%"
) else (
    echo ❌ Invalid choice. Please run the script again.
    pause
    exit /b 1
)

echo.
echo ✅ Simulation completed! Check your frontend for real-time updates.
pause
